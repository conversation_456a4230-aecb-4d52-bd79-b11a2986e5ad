<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">题库助手</string>
    <string name="btn_import_file">导入TXT题库文件</string>
    <string name="btn_start_floating">启动悬浮搜索</string>
    <string name="content_desc_collapse">收缩</string>
    <string name="content_desc_expand">展开搜索</string>
    <string name="content_desc_search_input">搜索输入框</string>
    <string name="error_file_read">文件读取失败</string>
    <string name="error_no_data">文件中没有找到有效的题目数据</string>
    <string name="error_unsupported_format">不支持的文件格式，请选择TXT文件</string>
    <string name="file_chooser_title">选择题库文件</string>
    <string name="main_title">题库助手</string>
    <string name="notification_channel_desc">保持悬浮搜索功能运行</string>
    <string name="notification_channel_name">悬浮搜索服务</string>
    <string name="notification_text">悬浮搜索正在运行</string>
    <string name="notification_title">悬浮搜索</string>
    <string name="permission_cancel">取消</string>
    <string name="permission_go_settings">去设置</string>
    <string name="permission_overlay_message">为了在其他应用上显示搜索界面，需要授予悬浮窗权限</string>
    <string name="permission_overlay_title">需要悬浮窗权限</string>
    <string name="search_hint">输入关键字搜索...</string>
    <string name="status_floating_started">悬浮搜索已启动</string>
    <string name="status_import_failed">导入失败：%s</string>
    <string name="status_import_first">请先导入题库文件</string>
    <string name="status_import_success">题库导入成功！共导入 %d 道题目</string>
    <string name="status_importing">正在导入题库...</string>
    <string name="status_initializing">正在初始化...</string>
    <string name="status_ready">准备就绪！可以启动悬浮搜索</string>
    <string name="toast_floating_started">悬浮搜索已启动，可以最小化应用</string>
    <string name="usage_instructions">使用说明：\n1. 点击「导入TXT题库文件」选择TXT格式的题库文件\n2. TXT文件格式：题目和答案用空行分隔，答案行以"答案"开头\n3. 文件编码建议使用UTF-8，支持自动检测GBK/GB2312\n4. 导入成功后点击「启动悬浮搜索」\n5. 悬浮搜索窗口将显示在屏幕上，可拖拽移动\n6. 在搜索框中输入关键字即可搜索题库内容\n7. 点击侧边可收缩悬浮窗\n\n作者：12626023</string>
    <style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style>
</resources>