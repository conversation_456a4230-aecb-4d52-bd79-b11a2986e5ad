<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Desktop\3\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Desktop\3\app\src\main\res"><file name="category_tag_background" path="E:\Desktop\3\app\src\main\res\drawable\category_tag_background.xml" qualifiers="" type="drawable"/><file name="collapsed_background" path="E:\Desktop\3\app\src\main\res\drawable\collapsed_background.xml" qualifiers="" type="drawable"/><file name="collapse_button_background" path="E:\Desktop\3\app\src\main\res\drawable\collapse_button_background.xml" qualifiers="" type="drawable"/><file name="expand_button_background" path="E:\Desktop\3\app\src\main\res\drawable\expand_button_background.xml" qualifiers="" type="drawable"/><file name="floating_background" path="E:\Desktop\3\app\src\main\res\drawable\floating_background.xml" qualifiers="" type="drawable"/><file name="ic_collapse" path="E:\Desktop\3\app\src\main\res\drawable\ic_collapse.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="E:\Desktop\3\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="E:\Desktop\3\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_search" path="E:\Desktop\3\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="info_background" path="E:\Desktop\3\app\src\main\res\drawable\info_background.xml" qualifiers="" type="drawable"/><file name="results_background" path="E:\Desktop\3\app\src\main\res\drawable\results_background.xml" qualifiers="" type="drawable"/><file name="result_item_background" path="E:\Desktop\3\app\src\main\res\drawable\result_item_background.xml" qualifiers="" type="drawable"/><file name="search_input_background" path="E:\Desktop\3\app\src\main\res\drawable\search_input_background.xml" qualifiers="" type="drawable"/><file name="status_background" path="E:\Desktop\3\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="title_bar_background" path="E:\Desktop\3\app\src\main\res\drawable\title_bar_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="E:\Desktop\3\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="collapsed_view" path="E:\Desktop\3\app\src\main\res\layout\collapsed_view.xml" qualifiers="" type="layout"/><file name="floating_search_view" path="E:\Desktop\3\app\src\main\res\layout\floating_search_view.xml" qualifiers="" type="layout"/><file name="search_result_item" path="E:\Desktop\3\app\src\main\res\layout\search_result_item.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="E:\Desktop\3\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="E:\Desktop\3\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="E:\Desktop\3\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Desktop\3\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Desktop\3\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Desktop\3\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Desktop\3\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Desktop\3\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Desktop\3\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Desktop\3\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="E:\Desktop\3\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="E:\Desktop\3\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="E:\Desktop\3\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="E:\Desktop\3\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">题库助手</string><string name="btn_start_floating">启动悬浮搜索</string><string name="file_chooser_title">选择题库文件</string><string name="notification_channel_desc">保持悬浮搜索功能运行</string><string name="content_desc_collapse">收缩</string><string name="toast_floating_started">悬浮搜索已启动，可以最小化应用</string><string name="usage_instructions">使用说明：\n1. 点击「导入TXT题库文件」选择TXT格式的题库文件\n2. TXT文件格式：题目和答案用空行分隔，答案行以"答案"开头\n3. 文件编码建议使用UTF-8，支持自动检测GBK/GB2312\n4. 导入成功后点击「启动悬浮搜索」\n5. 悬浮搜索窗口将显示在屏幕上，可拖拽移动\n6. 在搜索框中输入关键字即可搜索题库内容\n7. 点击侧边可收缩悬浮窗\n\n作者：12626023</string><string name="status_ready">准备就绪！可以启动悬浮搜索</string><string name="error_no_data">文件中没有找到有效的题目数据</string><string name="permission_overlay_title">需要悬浮窗权限</string><string name="btn_import_file">导入TXT题库文件</string><string name="main_title">题库助手</string><string name="content_desc_search_input">搜索输入框</string><string name="status_import_success">题库导入成功！共导入 %d 道题目</string><string name="status_floating_started">悬浮搜索已启动</string><string name="status_importing">正在导入题库...</string><string name="content_desc_expand">展开搜索</string><string name="notification_text">悬浮搜索正在运行</string><string name="search_hint">输入关键字搜索...</string><string name="status_import_first">请先导入题库文件</string><string name="status_initializing">正在初始化...</string><string name="permission_overlay_message">为了在其他应用上显示搜索界面，需要授予悬浮窗权限</string><string name="notification_title">悬浮搜索</string><string name="status_import_failed">导入失败：%s</string><string name="permission_go_settings">去设置</string><string name="error_unsupported_format">不支持的文件格式，请选择TXT文件</string><string name="permission_cancel">取消</string><string name="notification_channel_name">悬浮搜索服务</string><string name="error_file_read">文件读取失败</string></file><file path="E:\Desktop\3\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="E:\Desktop\3\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="E:\Desktop\3\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="E:\Desktop\3\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_resize" path="E:\Desktop\3\app\src\main\res\drawable\ic_resize.xml" qualifiers="" type="drawable"/><file name="resize_handle_background" path="E:\Desktop\3\app\src\main\res\drawable\resize_handle_background.xml" qualifiers="" type="drawable"/><file name="resize_bar_background" path="E:\Desktop\3\app\src\main\res\drawable\resize_bar_background.xml" qualifiers="" type="drawable"/><file name="scrollbar_thumb" path="E:\Desktop\3\app\src\main\res\drawable\scrollbar_thumb.xml" qualifiers="" type="drawable"/><file name="scrollbar_track" path="E:\Desktop\3\app\src\main\res\drawable\scrollbar_track.xml" qualifiers="" type="drawable"/><file name="ic_app_icon" path="E:\Desktop\3\app\src\main\res\drawable\ic_app_icon.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Desktop\3\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Desktop\3\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Desktop\3\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Desktop\3\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>