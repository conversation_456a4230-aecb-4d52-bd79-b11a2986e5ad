com.example.myapplication.app-lifecycle-livedata-2.6.2-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0a42f40601087ff8eca479462d57ad6a\transformed\lifecycle-livedata-2.6.2\res
com.example.myapplication.app-core-ktx-1.13.0-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\168d3440c81ebaa43678bbb2d1e68de0\transformed\core-ktx-1.13.0\res
com.example.myapplication.app-core-runtime-2.2.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\246e8b04052267f62e28436b7ab231ee\transformed\core-runtime-2.2.0\res
com.example.myapplication.app-emoji2-1.3.0-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\res
com.example.myapplication.app-appcompat-resources-1.7.1-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\2a41819dbb4c0977fcbf8178eab7ee88\transformed\appcompat-resources-1.7.1\res
com.example.myapplication.app-transition-1.5.0-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\2ae751e94a99a57446a8edec2e0d358f\transformed\transition-1.5.0\res
com.example.myapplication.app-fragment-1.5.4-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\378c8d085f1d64baefa52b7f8075b02c\transformed\fragment-1.5.4\res
com.example.myapplication.app-emoji2-views-helper-1.3.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\3a118886c109bf0fd6fd163018f8d353\transformed\emoji2-views-helper-1.3.0\res
com.example.myapplication.app-startup-runtime-1.1.1-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\4042815986da01ff554616c56cdfe138\transformed\startup-runtime-1.1.1\res
com.example.myapplication.app-appcompat-1.7.1-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\5635f18fca049daa08db71a81901940b\transformed\appcompat-1.7.1\res
com.example.myapplication.app-constraintlayout-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\64204c58b05a73d6deca9b80f398077c\transformed\constraintlayout-2.2.0\res
com.example.myapplication.app-core-1.13.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\res
com.example.myapplication.app-lifecycle-process-2.6.2-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\70c6f143380a3c49104048690c05f104\transformed\lifecycle-process-2.6.2\res
com.example.myapplication.app-lifecycle-viewmodel-2.6.2-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\786de5883d9351e363ac9992dfbb89bc\transformed\lifecycle-viewmodel-2.6.2\res
com.example.myapplication.app-profileinstaller-1.4.0-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\res
com.example.myapplication.app-customview-poolingcontainer-1.0.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\8918c5eb1f85ad15f008d7a2466e0d98\transformed\customview-poolingcontainer-1.0.0\res
com.example.myapplication.app-lifecycle-livedata-core-2.6.2-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\8d0ed3f1519b6c3b500ac13e357fd32e\transformed\lifecycle-livedata-core-2.6.2\res
com.example.myapplication.app-viewpager2-1.1.0-beta02-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\9b85ed7a72959eaa2fbebce00031ff9e\transformed\viewpager2-1.1.0-beta02\res
com.example.myapplication.app-annotation-experimental-1.4.0-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\9e85a5eff8580c8a3cde330a9d6179c3\transformed\annotation-experimental-1.4.0\res
com.example.myapplication.app-coordinatorlayout-1.1.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd8b4f4b5d548b1acac87c03e826de1\transformed\coordinatorlayout-1.1.0\res
com.example.myapplication.app-lifecycle-viewmodel-savedstate-2.6.2-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\a5e5f2714ccce1cc26a1bc2eea5d2d26\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.myapplication.app-lifecycle-runtime-2.6.2-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\aa14c02841fd08343c0115d25d58953c\transformed\lifecycle-runtime-2.6.2\res
com.example.myapplication.app-material-1.12.0-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\c4e70e887f52082eb2120633c7e9c4f9\transformed\material-1.12.0\res
com.example.myapplication.app-recyclerview-1.3.2-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\e8db674991d9ee636b14a5c207e1c3d0\transformed\recyclerview-1.3.2\res
com.example.myapplication.app-cardview-1.0.0-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\f2fb7ca6f7fec28577511d33f96ed4c4\transformed\cardview-1.0.0\res
com.example.myapplication.app-savedstate-1.2.1-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\f408e9a090472e104ede7e69859c1569\transformed\savedstate-1.2.1\res
com.example.myapplication.app-drawerlayout-1.1.1-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\f679fd0167934a21f1f5ad3b43fab21d\transformed\drawerlayout-1.1.1\res
com.example.myapplication.app-activity-1.9.3-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\f76a19a8e70d4b0df2911f6c34d5abc1\transformed\activity-1.9.3\res
com.example.myapplication.app-pngs-28 E:\Desktop\3\app\build\generated\res\pngs\debug
com.example.myapplication.app-resValues-29 E:\Desktop\3\app\build\generated\res\resValues\debug
com.example.myapplication.app-packageDebugResources-30 E:\Desktop\3\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.myapplication.app-packageDebugResources-31 E:\Desktop\3\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.myapplication.app-debug-32 E:\Desktop\3\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.myapplication.app-debug-33 E:\Desktop\3\app\src\debug\res
com.example.myapplication.app-main-34 E:\Desktop\3\app\src\main\res
