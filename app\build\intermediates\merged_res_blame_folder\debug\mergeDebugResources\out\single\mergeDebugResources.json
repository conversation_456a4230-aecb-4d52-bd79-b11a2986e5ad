[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_search_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\search_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\layout_floating_search_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\layout\\floating_search_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_category_tag_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\category_tag_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_content_background_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\content_background_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_collapse.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_collapse.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_resize_bar_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\resize_bar_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\layout_collapsed_view.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\layout\\collapsed_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_app_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_app_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_floating_background_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\floating_background_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_scrollbar_track.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\scrollbar_track.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "com.example.myapplication.app-debug-32:/layout_search_result_item.xml.flat", "source": "com.example.myapplication.app-main-34:/layout/search_result_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_search_input_background_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\search_input_background_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_resize_handle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\resize_handle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_expand_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\expand_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_info_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\info_background.xml"}, {"merged": "com.example.myapplication.app-debug-32:/layout_collapsed_view.xml.flat", "source": "com.example.myapplication.app-main-34:/layout/collapsed_view.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_result_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\result_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_collapsed_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\collapsed_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-anydpi-v26_ic_launcher_custom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-anydpi-v26\\ic_launcher_custom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_collapse_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\collapse_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_results_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\results_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_resize_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_resize_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_title_bar_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\title_bar_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_title_bar_background_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\title_bar_background_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_resize.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_resize.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\layout_search_result_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\layout\\search_result_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_floating_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\floating_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_ic_close_white.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\ic_close_white.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-32:\\drawable_scrollbar_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-34:\\drawable\\scrollbar_thumb.xml"}]