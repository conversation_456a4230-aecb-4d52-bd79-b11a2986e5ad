1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
12-->E:\Desktop\3\app\src\main\AndroidManifest.xml:6:5-78
12-->E:\Desktop\3\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->E:\Desktop\3\app\src\main\AndroidManifest.xml:7:5-80
13-->E:\Desktop\3\app\src\main\AndroidManifest.xml:7:22-77
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->E:\Desktop\3\app\src\main\AndroidManifest.xml:8:5-9:40
14-->E:\Desktop\3\app\src\main\AndroidManifest.xml:8:22-79
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->E:\Desktop\3\app\src\main\AndroidManifest.xml:10:5-77
15-->E:\Desktop\3\app\src\main\AndroidManifest.xml:10:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->E:\Desktop\3\app\src\main\AndroidManifest.xml:11:5-89
16-->E:\Desktop\3\app\src\main\AndroidManifest.xml:11:22-86
17
18    <permission
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->E:\Desktop\3\app\src\main\AndroidManifest.xml:13:5-46:19
25        android:allowBackup="true"
25-->E:\Desktop\3\app\src\main\AndroidManifest.xml:14:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6648a275815469a6a3eb7b24cdd05ab9\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
27        android:dataExtractionRules="@xml/data_extraction_rules"
27-->E:\Desktop\3\app\src\main\AndroidManifest.xml:15:9-65
28        android:debuggable="true"
29        android:extractNativeLibs="false"
30        android:fullBackupContent="@xml/backup_rules"
30-->E:\Desktop\3\app\src\main\AndroidManifest.xml:16:9-54
31        android:icon="@drawable/ic_app_icon"
31-->E:\Desktop\3\app\src\main\AndroidManifest.xml:17:9-45
32        android:label="@string/app_name"
32-->E:\Desktop\3\app\src\main\AndroidManifest.xml:18:9-41
33        android:requestLegacyExternalStorage="true"
33-->E:\Desktop\3\app\src\main\AndroidManifest.xml:22:9-52
34        android:roundIcon="@drawable/ic_app_icon"
34-->E:\Desktop\3\app\src\main\AndroidManifest.xml:19:9-50
35        android:supportsRtl="true"
35-->E:\Desktop\3\app\src\main\AndroidManifest.xml:20:9-35
36        android:testOnly="true"
37        android:theme="@style/Theme.MyApplication" >
37-->E:\Desktop\3\app\src\main\AndroidManifest.xml:21:9-51
38
39        <!-- 主Activity -->
40        <activity
40-->E:\Desktop\3\app\src\main\AndroidManifest.xml:25:9-33:20
41            android:name="com.example.myapplication.MainActivity"
41-->E:\Desktop\3\app\src\main\AndroidManifest.xml:26:13-41
42            android:exported="true"
42-->E:\Desktop\3\app\src\main\AndroidManifest.xml:27:13-36
43            android:theme="@style/Theme.MyApplication" >
43-->E:\Desktop\3\app\src\main\AndroidManifest.xml:28:13-55
44            <intent-filter>
44-->E:\Desktop\3\app\src\main\AndroidManifest.xml:29:13-32:29
45                <action android:name="android.intent.action.MAIN" />
45-->E:\Desktop\3\app\src\main\AndroidManifest.xml:30:17-69
45-->E:\Desktop\3\app\src\main\AndroidManifest.xml:30:25-66
46
47                <category android:name="android.intent.category.LAUNCHER" />
47-->E:\Desktop\3\app\src\main\AndroidManifest.xml:31:17-77
47-->E:\Desktop\3\app\src\main\AndroidManifest.xml:31:27-74
48            </intent-filter>
49        </activity>
50
51        <!-- 悬浮搜索服务 -->
52        <service
52-->E:\Desktop\3\app\src\main\AndroidManifest.xml:36:9-44:19
53            android:name="com.example.myapplication.FloatingSearchService"
53-->E:\Desktop\3\app\src\main\AndroidManifest.xml:37:13-50
54            android:enabled="true"
54-->E:\Desktop\3\app\src\main\AndroidManifest.xml:38:13-35
55            android:exported="false"
55-->E:\Desktop\3\app\src\main\AndroidManifest.xml:39:13-37
56            android:foregroundServiceType="specialUse" >
56-->E:\Desktop\3\app\src\main\AndroidManifest.xml:40:13-55
57            <property
57-->E:\Desktop\3\app\src\main\AndroidManifest.xml:41:13-43:51
58                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
58-->E:\Desktop\3\app\src\main\AndroidManifest.xml:42:17-76
59                android:value="floating_search" />
59-->E:\Desktop\3\app\src\main\AndroidManifest.xml:43:17-48
60        </service>
61
62        <provider
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
63            android:name="androidx.startup.InitializationProvider"
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
64            android:authorities="com.example.myapplication.androidx-startup"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
65            android:exported="false" >
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\26cc4e939715710c883df7a837cb91fa\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70c6f143380a3c49104048690c05f104\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70c6f143380a3c49104048690c05f104\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\70c6f143380a3c49104048690c05f104\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <receiver
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
78            android:name="androidx.profileinstaller.ProfileInstallReceiver"
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
79            android:directBootAware="false"
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
80            android:enabled="true"
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
81            android:exported="true"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
82            android:permission="android.permission.DUMP" >
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
84                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
84-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
87                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
90                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
93                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
93-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bf49252a30c158c03d7c64f6c6d482\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
94            </intent-filter>
95        </receiver>
96    </application>
97
98</manifest>
