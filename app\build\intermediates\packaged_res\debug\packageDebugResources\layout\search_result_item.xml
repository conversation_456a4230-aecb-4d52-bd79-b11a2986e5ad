<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp"
    android:background="@drawable/result_item_background"
    android:layout_marginBottom="4dp">

    <!-- 分类标签 -->
    <TextView
        android:id="@+id/categoryText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="10sp"
        android:textColor="@android:color/white"
        android:background="@drawable/category_tag_background"
        android:padding="4dp"
        android:layout_marginBottom="4dp"
        android:visibility="gone" />

    <!-- 题目内容 -->
    <TextView
        android:id="@+id/questionText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="13sp"
        android:textColor="#FF000000"
        android:textStyle="bold"
        android:lineSpacingExtra="2dp"
        android:layout_marginBottom="6dp" />

    <!-- 答案内容 -->
    <TextView
        android:id="@+id/answerText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="11sp"
        android:textColor="#FF4CAF50"
        android:lineSpacingExtra="2dp"
        android:maxLines="3"
        android:ellipsize="end"
        android:visibility="gone" />

</LinearLayout>
