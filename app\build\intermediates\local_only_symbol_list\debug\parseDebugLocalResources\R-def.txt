R_DEF: Internal format may change without notice
local
color black
color purple_200
color purple_500
color purple_700
color teal_200
color teal_700
color white
drawable category_tag_background
drawable collapse_button_background
drawable collapsed_background
drawable expand_button_background
drawable floating_background
drawable ic_app_icon
drawable ic_collapse
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_resize
drawable ic_search
drawable info_background
drawable resize_bar_background
drawable resize_handle_background
drawable result_item_background
drawable results_background
drawable scrollbar_thumb
drawable scrollbar_track
drawable search_input_background
drawable status_background
drawable title_bar_background
id answerText
id categoryText
id collapseButton
id expandButton
id importButton
id questionText
id resultsContainer
id resultsRecyclerView
id resultsResizeHandle
id searchEditText
id startFloatingButton
id statusText
id titleBar
layout activity_main
layout collapsed_view
layout floating_search_view
layout search_result_item
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string btn_import_file
string btn_start_floating
string content_desc_collapse
string content_desc_expand
string content_desc_search_input
string error_file_read
string error_no_data
string error_unsupported_format
string file_chooser_title
string main_title
string notification_channel_desc
string notification_channel_name
string notification_text
string notification_title
string permission_cancel
string permission_go_settings
string permission_overlay_message
string permission_overlay_title
string search_hint
string status_floating_started
string status_import_failed
string status_import_first
string status_import_success
string status_importing
string status_initializing
string status_ready
string toast_floating_started
string usage_instructions
style Theme.MyApplication
xml backup_rules
xml data_extraction_rules
