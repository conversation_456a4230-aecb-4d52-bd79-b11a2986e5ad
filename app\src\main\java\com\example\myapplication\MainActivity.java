package com.example.myapplication;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.annotation.NonNull;

public class MainActivity extends AppCompatActivity {

    private static final int PERMISSION_REQUEST_CODE = 1001;
    private static boolean isServiceRunning = false;
    
    private TextView statusText;
    private Button importButton;
    private Button startFloatingButton;
    
    private QuestionDatabase questionDatabase;
    private FileImporter fileImporter;
    
    // 文件选择器
    private ActivityResultLauncher<Intent> filePickerLauncher;
    
    // 悬浮窗权限请求
    private ActivityResultLauncher<Intent> overlayPermissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        initComponents();
        initLaunchers();
        checkPermissions();
    }
    
    private void initViews() {
        statusText = findViewById(R.id.statusText);
        importButton = findViewById(R.id.importButton);
        startFloatingButton = findViewById(R.id.startFloatingButton);
        
        importButton.setOnClickListener(v -> openFilePicker());
        startFloatingButton.setOnClickListener(v -> startFloatingService());
        
        // 初始状态
        startFloatingButton.setEnabled(false);
        updateStatus(getString(R.string.status_import_first));
    }
    
    private void initComponents() {
        questionDatabase = new QuestionDatabase(this);
        fileImporter = new FileImporter(this, questionDatabase);
    }
    
    private void initLaunchers() {
        // 文件选择器结果处理
        filePickerLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                    Uri uri = result.getData().getData();
                    if (uri != null) {
                        importFile(uri);
                    }
                }
            }
        );
        
        // 悬浮窗权限结果处理
        overlayPermissionLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (Settings.canDrawOverlays(this)) {
                    updateStatus("悬浮窗权限已获取");
                    checkIfCanStartFloating();
                } else {
                    updateStatus("需要悬浮窗权限才能使用悬浮搜索功能");
                }
            }
        );
    }
    
    private void checkPermissions() {
        // 检查文件读取权限 (minSdk 24 >= M(23), 所以总是需要检查)
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                PERMISSION_REQUEST_CODE);
        }

        // 检查悬浮窗权限 (minSdk 24 >= M(23), 所以总是需要检查)
        if (!Settings.canDrawOverlays(this)) {
            showOverlayPermissionDialog();
        }
    }
    
    private void showOverlayPermissionDialog() {
        new AlertDialog.Builder(this)
            .setTitle(R.string.permission_overlay_title)
            .setMessage(R.string.permission_overlay_message)
            .setPositiveButton(R.string.permission_go_settings, (dialog, which) -> requestOverlayPermission())
            .setNegativeButton(R.string.permission_cancel, null)
            .show();
    }
    
    private void requestOverlayPermission() {
        // minSdk 24 >= M(23), 所以总是支持悬浮窗权限管理
        Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:" + getPackageName()));
        overlayPermissionLauncher.launch(intent);
    }
    
    private void openFilePicker() {
        Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
        intent.setType("*/*"); // 允许选择所有文件类型
        intent.addCategory(Intent.CATEGORY_OPENABLE);

        // 尝试指定文本文件类型
        String[] mimeTypes = {"text/plain", "text/*", "*/*"};
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes);

        filePickerLauncher.launch(Intent.createChooser(intent, "选择TXT题库文件（支持所有文本文件）"));
    }
    
    private void importFile(Uri uri) {
        updateStatus(getString(R.string.status_importing));
        importButton.setEnabled(false);

        fileImporter.importFile(uri, new FileImporter.ImportCallback() {
            @Override
            public void onSuccess(int questionCount) {
                runOnUiThread(() -> {
                    updateStatus(getString(R.string.status_import_success, questionCount));
                    importButton.setEnabled(true);

                    // 显示导入成功弹窗
                    new androidx.appcompat.app.AlertDialog.Builder(MainActivity.this)
                        .setTitle("导入成功")
                        .setMessage("恭喜！成功导入 " + questionCount + " 道题目到题库中。\n\n现在可以启动悬浮搜索功能了！")
                        .setPositiveButton("确定", null)
                        .setIcon(android.R.drawable.ic_dialog_info)
                        .show();

                    checkIfCanStartFloating();
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    updateStatus("导入失败：" + error);
                    importButton.setEnabled(true);

                    // 显示详细的错误信息和格式说明
                    new androidx.appcompat.app.AlertDialog.Builder(MainActivity.this)
                        .setTitle("导入失败")
                        .setMessage(error + "\n\n正确的TXT格式示例：\n1.def 文件名称检查(文件夹路径, 日志文件夹路径, COV文件名称, 机种名称):\n\n答案3\n\n2.def 文件名称检查\n\n答案6\n\n注意事项：\n• 题目和答案用空行分隔\n• 答案行以'答案'开头\n• 文件编码建议使用UTF-8")
                        .setPositiveButton("确定", null)
                        .show();
                });
            }
        });
    }
    
    private void checkIfCanStartFloating() {
        boolean hasQuestions = questionDatabase.getQuestionCount() > 0;
        // minSdk 24 >= M(23), 所以总是需要检查悬浮窗权限
        boolean hasOverlayPermission = Settings.canDrawOverlays(this);

        if (hasQuestions && hasOverlayPermission) {
            startFloatingButton.setEnabled(true);
            updateStatus(getString(R.string.status_ready));
        }
    }
    
    private void startFloatingService() {
        if (isServiceRunning) {
            Toast.makeText(this, "悬浮搜索已经在运行中", Toast.LENGTH_SHORT).show();
            return;
        }

        Intent serviceIntent = new Intent(this, FloatingSearchService.class);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            startForegroundService(serviceIntent);
        } else {
            startService(serviceIntent);
        }

        isServiceRunning = true;
        startFloatingButton.setText("悬浮搜索运行中");
        startFloatingButton.setEnabled(false);

        updateStatus(getString(R.string.status_floating_started));
        Toast.makeText(this, getString(R.string.toast_floating_started), Toast.LENGTH_LONG).show();

        // 最小化应用
        moveTaskToBack(true);
    }
    
    private void updateStatus(String status) {
        statusText.setText(status);
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                updateStatus("文件读取权限已获取");
            } else {
                updateStatus("需要文件读取权限才能导入题库");
            }
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        checkIfCanStartFloating();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (fileImporter != null) {
            fileImporter.destroy();
        }
    }
}
