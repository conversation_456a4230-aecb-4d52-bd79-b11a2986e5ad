package com.example.myapplication;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class SearchResultAdapter extends RecyclerView.Adapter<SearchResultAdapter.ViewHolder> {

    private static final String TAG = "SearchResultAdapter";
    private List<QuestionDatabase.Question> questions;
    
    public SearchResultAdapter(List<QuestionDatabase.Question> questions) {
        this.questions = questions;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.search_result_item, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        QuestionDatabase.Question question = questions.get(position);

        String questionText = question.getQuestion();
        String answerText = question.getAnswer();

        Log.d(TAG, "显示题目: " + questionText + " | 答案: " + answerText);

        holder.questionText.setText(questionText);

        // 显示答案（如果有）
        if (answerText != null && !answerText.trim().isEmpty()) {
            // 格式化答案显示
            String formattedAnswer = "答案：" + answerText;
            holder.answerText.setText(formattedAnswer);
            holder.answerText.setVisibility(View.VISIBLE);
        } else {
            holder.answerText.setVisibility(View.GONE);
        }
        
        // 隐藏分类标签
        holder.categoryText.setVisibility(View.GONE);
        
        // 点击展开/收缩答案
        holder.itemView.setOnClickListener(v -> {
            if (holder.answerText.getVisibility() == View.VISIBLE) {
                if (holder.answerText.getMaxLines() == 3) {
                    holder.answerText.setMaxLines(Integer.MAX_VALUE);
                } else {
                    holder.answerText.setMaxLines(3);
                }
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return questions.size();
    }
    
    @SuppressWarnings("NotifyDataSetChanged")
    public void updateResults(List<QuestionDatabase.Question> newQuestions) {
        this.questions = newQuestions;
        notifyDataSetChanged();
    }
    
    /**
     * ViewHolder for search result items
     */
    @SuppressWarnings({"WeakerAccess", "CanBeFinal", "unused"})
    static class ViewHolder extends RecyclerView.ViewHolder {
        final TextView questionText;
        final TextView answerText;
        final TextView categoryText;

        ViewHolder(View itemView) {
            super(itemView);
            questionText = itemView.findViewById(R.id.questionText);
            answerText = itemView.findViewById(R.id.answerText);
            categoryText = itemView.findViewById(R.id.categoryText);
        }
    }
}
