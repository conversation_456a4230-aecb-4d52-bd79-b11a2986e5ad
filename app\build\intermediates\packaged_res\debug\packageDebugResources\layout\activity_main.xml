<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center">

    <!-- 应用标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/main_title"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/design_default_color_primary"
        android:layout_marginBottom="32dp" />

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/statusText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/status_initializing"
        android:textSize="16sp"
        android:textAlignment="center"
        android:padding="16dp"
        android:background="@drawable/status_background"
        android:layout_marginBottom="32dp" />

    <!-- 导入题库按钮 -->
    <Button
        android:id="@+id/importButton"
        style="@style/Widget.Material3.Button"
        android:layout_width="wrap_content"
        android:layout_height="56dp"
        android:layout_marginBottom="16dp"
        android:text="@string/btn_import_file"
        android:textSize="18sp" />

    <!-- 启动悬浮搜索按钮 -->
    <Button
        android:id="@+id/startFloatingButton"
        style="@style/Widget.Material3.Button.UnelevatedButton"
        android:layout_width="185dp"
        android:layout_height="56dp"
        android:layout_marginBottom="32dp"
        android:text="@string/btn_start_floating"
        android:textSize="18sp" />

    <!-- 使用说明 -->
    <TextView
        android:layout_width="346dp"
        android:layout_height="wrap_content"
        android:background="@drawable/info_background"
        android:lineSpacingExtra="4dp"
        android:padding="16dp"
        android:text="@string/usage_instructions"
        android:textColor="@color/design_default_color_on_surface"
        android:textSize="14sp" />

</LinearLayout>
