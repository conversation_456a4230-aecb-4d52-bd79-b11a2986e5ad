package com.example.myapplication;

import android.content.Context;
import android.net.Uri;
import android.util.Log;

// Removed CSV and POI imports - using plain text processing

import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class FileImporter {
    
    private static final String TAG = "FileImporter";
    
    private final Context context;
    private final QuestionDatabase database;
    private final ExecutorService executor;
    
    public interface ImportCallback {
        void onSuccess(int questionCount);
        void onError(String error);
    }
    
    public FileImporter(Context context, QuestionDatabase database) {
        this.context = context;
        this.database = database;
        this.executor = Executors.newSingleThreadExecutor();
    }
    
    public void importFile(Uri uri, ImportCallback callback) {
        executor.execute(() -> {
            try {
                String fileName = getFileName(uri);
                Log.d(TAG, "开始导入文件: " + fileName);
                List<QuestionDatabase.Question> questions;

                // 尝试导入文本文件，不严格检查扩展名
                // 因为某些文件选择器可能返回特殊的URI格式
                if (fileName.toLowerCase().endsWith(".txt") ||
                    fileName.toLowerCase().contains("txt") ||
                    !fileName.contains(".") || // 没有扩展名的文件也尝试作为文本处理
                    fileName.startsWith("msf:") || // 处理特殊URI格式
                    fileName.startsWith("content:")) {

                    Log.d(TAG, "尝试作为文本文件导入: " + fileName);
                    questions = importTxtFile(uri);
                } else {
                    Log.e(TAG, "不支持的文件格式: " + fileName);
                    callback.onError("目前只支持TXT格式文件，请选择TXT文件\n当前文件: " + fileName);
                    return;
                }

                if (questions.isEmpty()) {
                    Log.e(TAG, "没有找到有效的题目数据");
                    callback.onError("文件中没有找到有效的题目数据。\n\n请检查TXT文件格式：\n• 题目和答案用空行分隔\n• 答案行以'答案'开头\n• 确保文件编码为UTF-8\n\n请查看Logcat日志获取详细信息");
                    return;
                }

                // 清空现有数据并导入新数据
                database.clearAllQuestions();
                database.addQuestions(questions);

                Log.d(TAG, "题库导入成功，共 " + questions.size() + " 道题目");
                callback.onSuccess(questions.size());

            } catch (Exception e) {
                Log.e(TAG, "导入文件失败", e);
                callback.onError("文件读取失败：" + e.getMessage() + "\n请确保文件格式正确且有读取权限");
            }
        });
    }
    
    private List<QuestionDatabase.Question> importTxtFile(Uri uri) throws Exception {
        List<QuestionDatabase.Question> questions = new ArrayList<>();

        String fileContent;

        // 首先尝试UTF-8编码
        try (InputStream inputStream = context.getContentResolver().openInputStream(uri)) {
            InputStreamReader reader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            StringBuilder content = new StringBuilder();
            char[] buffer = new char[1024];
            int length;

            while ((length = reader.read(buffer)) != -1) {
                content.append(buffer, 0, length);
            }
            reader.close();
            fileContent = content.toString();

            Log.d(TAG, "UTF-8读取成功，内容长度: " + fileContent.length());
        } catch (Exception e) {
            Log.w(TAG, "UTF-8读取失败，尝试GBK", e);

            // 如果UTF-8失败，尝试GBK
            try (InputStream inputStream2 = context.getContentResolver().openInputStream(uri)) {
                InputStreamReader reader = new InputStreamReader(inputStream2, "GBK");
                StringBuilder content = new StringBuilder();
                char[] buffer = new char[1024];
                int length;

                while ((length = reader.read(buffer)) != -1) {
                    content.append(buffer, 0, length);
                }
                reader.close();
                fileContent = content.toString();

                Log.d(TAG, "GBK读取成功，内容长度: " + fileContent.length());
            } catch (Exception gbkException) {
                Log.e(TAG, "GBK读取也失败", gbkException);
                throw new Exception("无法读取文件内容，UTF-8和GBK编码都失败", gbkException);
            }
        }

        if (fileContent.isEmpty()) {
            throw new Exception("无法读取文件内容");
        }

        // 按照您的格式解析文本
        Log.d(TAG, "文件内容长度: " + fileContent.length());
        Log.d(TAG, "文件内容预览: " + fileContent.substring(0, Math.min(200, fileContent.length())));

            // 简化解析逻辑：按空行分割成块
            String[] blocks = fileContent.split("\n\\s*\n"); // 匹配一个或多个空行
            Log.d(TAG, "分割后的块数: " + blocks.length);

            for (int i = 0; i < blocks.length; i++) {
                String block = blocks[i].trim();
                if (block.isEmpty()) {
                    continue;
                }

                Log.d(TAG, "处理块 " + i + ": " + block);

                // 分割每个块的行
                String[] lines = block.split("\n");
                String questionText = "";
                String answerText = "";

                // 查找题目和答案
                boolean foundQuestion = false;
                for (String line : lines) {
                    line = line.trim();
                    if (line.isEmpty()) continue;

                    if (line.startsWith("答案")) {
                        // 提取答案内容
                        if (line.length() > 2) {
                            // 如果答案在同一行：答案6
                            answerText = line.substring(2).trim();
                        }
                        Log.d(TAG, "找到答案行: " + line + " -> 提取答案: " + answerText);
                    } else if (!foundQuestion) {
                        // 第一个非答案行作为题目
                        questionText = line;
                        foundQuestion = true;
                        Log.d(TAG, "找到题目: " + questionText);
                    } else if (answerText.isEmpty()) {
                        // 如果还没有找到答案，这可能是答案内容
                        answerText = line;
                        Log.d(TAG, "找到答案内容: " + answerText);
                    }
                }

                // 如果找到了题目，创建问题对象
                if (!questionText.isEmpty()) {
                    QuestionDatabase.Question question = createQuestion(questionText, answerText);
                    questions.add(question);
                    Log.d(TAG, "成功导入题目: " + questionText + " | 答案: " + answerText);
                }
            }

            // 如果没有找到任何题目，尝试另一种解析方式
            if (questions.isEmpty()) {
                Log.d(TAG, "第一种方式没有找到题目，尝试逐行解析");
                String[] allLines = fileContent.split("\n");
                String currentQuestion = "";
                String currentAnswer = "";

                for (int i = 0; i < allLines.length; i++) {
                    String line = allLines[i].trim();
                    if (line.isEmpty()) {
                        // 空行，如果有完整的题目和答案就保存
                        if (!currentQuestion.isEmpty() && !currentAnswer.isEmpty()) {
                            QuestionDatabase.Question question = new QuestionDatabase.Question();
                            question.setQuestion(currentQuestion);
                            question.setAnswer(currentAnswer);
                            question.setCategory("其他");
                            questions.add(question);
                            Log.d(TAG, "备用方式导入: " + currentQuestion + " | " + currentAnswer);
                            currentQuestion = "";
                            currentAnswer = "";
                        }
                        continue;
                    }

                    if (line.startsWith("答案")) {
                        // 提取答案
                        if (line.length() > 2) {
                            currentAnswer = line.substring(2).trim();
                        } else if (i + 1 < allLines.length) {
                            // 答案可能在下一行
                            currentAnswer = allLines[i + 1].trim();
                            i++; // 跳过下一行
                        }
                        Log.d(TAG, "备用方式找到答案: " + currentAnswer);
                    } else if (currentQuestion.isEmpty()) {
                        currentQuestion = line;
                        Log.d(TAG, "备用方式找到题目: " + currentQuestion);
                    }
                }

                // 处理最后一个题目
                if (!currentQuestion.isEmpty() && !currentAnswer.isEmpty()) {
                    QuestionDatabase.Question question = createQuestion(currentQuestion, currentAnswer);
                    questions.add(question);
                    Log.d(TAG, "备用方式导入最后一个: " + currentQuestion + " | " + currentAnswer);
                }
            }

        Log.d(TAG, "TXT导入完成，共导入 " + questions.size() + " 道题目");
        return questions;
    }
    
    // Excel support removed to avoid dependency conflicts
    // Users can convert Excel files to CSV format for import


    
    private String getFileName(Uri uri) {
        // 首先尝试从URI中获取显示名称
        try {
            android.database.Cursor cursor = context.getContentResolver().query(
                uri, null, null, null, null);
            if (cursor != null) {
                int nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME);
                if (nameIndex >= 0 && cursor.moveToFirst()) {
                    String displayName = cursor.getString(nameIndex);
                    cursor.close();
                    if (displayName != null && !displayName.isEmpty()) {
                        Log.d(TAG, "从ContentResolver获取文件名: " + displayName);
                        return displayName;
                    }
                }
                cursor.close();
            }
        } catch (Exception e) {
            Log.w(TAG, "无法从ContentResolver获取文件名", e);
        }

        // 备用方法：从URI路径获取
        String path = uri.getPath();
        if (path != null) {
            int lastSlash = path.lastIndexOf('/');
            if (lastSlash != -1) {
                String fileName = path.substring(lastSlash + 1);
                Log.d(TAG, "从URI路径获取文件名: " + fileName);
                return fileName;
            }
        }

        // 最后备用：使用URI字符串
        String uriString = uri.toString();
        Log.d(TAG, "使用URI字符串作为文件名: " + uriString);
        return uriString;
    }
    
    /**
     * 创建问题对象并设置分类
     */
    private QuestionDatabase.Question createQuestion(String questionText, String answerText) {
        QuestionDatabase.Question question = new QuestionDatabase.Question();
        question.setQuestion(questionText);
        question.setAnswer(answerText.isEmpty() ? "无答案" : answerText);

        // 自动分类
        if (questionText.contains("def ")) {
            question.setCategory("编程");
        } else if (questionText.contains("文件")) {
            question.setCategory("文件操作");
        } else {
            question.setCategory("其他");
        }

        return question;
    }

    public void destroy() {
        if (executor != null) {
            executor.shutdown();
        }
    }
}
