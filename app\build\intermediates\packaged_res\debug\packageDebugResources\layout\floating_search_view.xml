<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="200dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/floating_background"
    android:padding="8dp">

    <!-- 标题栏（用于拖拽） -->
    <LinearLayout
        android:id="@+id/titleBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="@drawable/title_bar_background">

        <!-- 搜索框 -->
        <EditText
            android:id="@+id/searchEditText"
            android:layout_width="0dp"
            android:layout_height="32dp"
            android:layout_weight="1"
            android:hint="@string/search_hint"
            android:textSize="12sp"
            android:textColor="@android:color/black"
            android:textColorHint="#666666"
            android:background="@drawable/search_input_background"
            android:padding="6dp"
            android:singleLine="true"
            android:imeOptions="actionSearch"
            android:contentDescription="@string/content_desc_search_input"
            android:importantForAutofill="no"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:clickable="true" />

        <!-- 收缩按钮 -->
        <ImageView
            android:id="@+id/collapseButton"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_collapse"
            android:background="@drawable/collapse_button_background"
            android:padding="6dp"
            android:scaleType="centerInside"
            android:contentDescription="@string/content_desc_collapse" />

    </LinearLayout>

    <!-- 搜索结果容器 -->
    <LinearLayout
        android:id="@+id/resultsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="vertical"
        android:background="@drawable/results_background"
        android:visibility="gone">

        <!-- 搜索结果列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/resultsRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:scrollbars="vertical"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarThumbVertical="@drawable/scrollbar_thumb"
            android:scrollbarTrackVertical="@drawable/scrollbar_track"
            android:fadeScrollbars="false"
            android:scrollbarSize="6dp"
            android:scrollbarFadeDuration="300"
            android:scrollbarDefaultDelayBeforeFade="1000"
            android:background="@android:color/transparent"
            android:padding="2dp"
            android:clipToPadding="false" />

        <!-- 结果区域调整手柄 -->
        <View
            android:id="@+id/resultsResizeHandle"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="@drawable/resize_bar_background"
            android:layout_marginTop="2dp" />

    </LinearLayout>

</LinearLayout>
