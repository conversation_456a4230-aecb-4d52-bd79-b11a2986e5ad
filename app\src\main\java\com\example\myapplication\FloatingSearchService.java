package com.example.myapplication;

import android.annotation.SuppressLint;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.IBinder;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;


import androidx.core.app.NotificationCompat;

public class FloatingSearchService extends Service {

    private static final String CHANNEL_ID = "FloatingSearchChannel";
    private static final int NOTIFICATION_ID = 1;
    private static final int FLOATING_WIDTH_DP = 200;
    private static final int COLLAPSED_Y_POSITION = 200;
    
    private WindowManager windowManager;
    private FloatingSearchView floatingSearchView;
    private View collapsedView;

    private WindowManager.LayoutParams collapsedParams;
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        createNotificationChannel();
        
        createFloatingViews();
        showFloatingView();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        startForeground(NOTIFICATION_ID, createNotification());
        return START_STICKY;
    }
    
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                getString(R.string.notification_channel_name),
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription(getString(R.string.notification_channel_desc));
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            manager.createNotificationChannel(channel);
        }
    }
    
    private Notification createNotification() {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.notification_title))
            .setContentText(getString(R.string.notification_text))
            .setSmallIcon(R.mipmap.ic_launcher)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build();
    }
    
    @SuppressLint("InflateParams")
    private void createFloatingViews() {
        // 创建悬浮搜索视图
        floatingSearchView = new FloatingSearchView(this);
        floatingSearchView.setOnCollapseListener(() -> {
            hideFloatingView();
            showCollapsedView();
        });
        
        // 创建收缩视图
        LayoutInflater inflater = LayoutInflater.from(this);
        collapsedView = inflater.inflate(R.layout.collapsed_view, null);
        
        setupDragListener(collapsedView);
    }
    
    private void showFloatingView() {
        if (floatingSearchView.getParent() != null) {
            windowManager.removeView(floatingSearchView);
        }

        WindowManager.LayoutParams floatingParams = createFloatingParams();
        windowManager.addView(floatingSearchView, floatingParams);
    }

    private WindowManager.LayoutParams createFloatingParams() {
        int initialWidth = dpToPx(FLOATING_WIDTH_DP);
        WindowManager.LayoutParams params = createBaseLayoutParams(
                initialWidth,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
        );

        setFloatingPosition(params, initialWidth);
        return params;
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return (int) (dp * density);
    }

    private WindowManager.LayoutParams createBaseLayoutParams(int width, int height, int flags) {
        return new WindowManager.LayoutParams(
                width,
                height,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ?
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY :
                        WindowManager.LayoutParams.TYPE_PHONE,
                flags,
                PixelFormat.TRANSLUCENT
        );
    }

    private void setFloatingPosition(WindowManager.LayoutParams params, int width) {
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int screenHeight = getResources().getDisplayMetrics().heightPixels;

        params.gravity = Gravity.TOP | Gravity.START;
        params.x = (screenWidth - width) / 2; // 居中显示
        params.y = screenHeight / 3; // 在屏幕上方1/3处显示
    }

    private WindowManager.LayoutParams createCollapsedParams() {
        WindowManager.LayoutParams params = createBaseLayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        );

        params.gravity = Gravity.TOP | Gravity.START;
        params.x = 0;
        params.y = COLLAPSED_Y_POSITION;

        return params;
    }

    private void hideFloatingView() {
        if (floatingSearchView != null) {
            floatingSearchView.hideKeyboard(); // 隐藏键盘
        }
        if (floatingSearchView != null && floatingSearchView.getParent() != null) {
            windowManager.removeView(floatingSearchView);
        }
    }
    
    private void showCollapsedView() {
        if (collapsedView != null && collapsedView.getParent() != null) {
            windowManager.removeView(collapsedView);
        }

        collapsedParams = createCollapsedParams();
        windowManager.addView(collapsedView, collapsedParams);
    }
    
    private void hideCollapsedView() {
        if (collapsedView.getParent() != null) {
            windowManager.removeView(collapsedView);
        }
    }
    
    private void setupDragListener(View view) {
        view.setOnTouchListener(new View.OnTouchListener() {
            private int initialX;
            private int initialY;
            private float initialTouchX;
            private float initialTouchY;
            private boolean isDragging = false;
            private static final int TOUCH_THRESHOLD = 15; // 移动阈值

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 确保使用TOP|START gravity进行拖拽
                        collapsedParams.gravity = Gravity.TOP | Gravity.START;

                        initialX = collapsedParams.x;
                        initialY = collapsedParams.y;
                        initialTouchX = event.getRawX();
                        initialTouchY = event.getRawY();
                        isDragging = false;
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        float deltaX = event.getRawX() - initialTouchX;
                        float deltaY = event.getRawY() - initialTouchY;

                        // 检查是否超过移动阈值
                        if (!isDragging && (Math.abs(deltaX) > TOUCH_THRESHOLD || Math.abs(deltaY) > TOUCH_THRESHOLD)) {
                            isDragging = true;
                        }

                        if (isDragging) {
                            collapsedParams.x = initialX + (int) deltaX;
                            collapsedParams.y = initialY + (int) deltaY;

                            // 限制在屏幕范围内
                            int screenWidth = getResources().getDisplayMetrics().widthPixels;
                            int screenHeight = getResources().getDisplayMetrics().heightPixels;

                            if (collapsedParams.x < 0) collapsedParams.x = 0;
                            if (collapsedParams.y < 0) collapsedParams.y = 0;
                            if (collapsedParams.x > screenWidth - view.getWidth()) {
                                collapsedParams.x = screenWidth - view.getWidth();
                            }
                            if (collapsedParams.y > screenHeight - view.getHeight()) {
                                collapsedParams.y = screenHeight - view.getHeight();
                            }

                            windowManager.updateViewLayout(collapsedView, collapsedParams);
                        }
                        return isDragging;

                    case MotionEvent.ACTION_UP:
                        if (isDragging) {
                            // 吸附到屏幕边缘
                            snapToEdge();
                        } else {
                            // 如果没有拖拽，执行点击事件
                            v.performClick();
                            hideCollapsedView();
                            showFloatingView();
                        }
                        return true;
                }
                return false;
            }
        });
    }
    
    private void snapToEdge() {
        // 获取屏幕宽度
        int screenWidth = getResources().getDisplayMetrics().widthPixels;

        // 判断靠近哪一边
        if (collapsedParams.x < screenWidth / 2) {
            // 靠近左边
            collapsedParams.gravity = Gravity.TOP | Gravity.START;
        } else {
            // 靠近右边
            collapsedParams.gravity = Gravity.TOP | Gravity.END;
        }
        collapsedParams.x = 0;

        windowManager.updateViewLayout(collapsedView, collapsedParams);
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        
        if (floatingSearchView != null && floatingSearchView.getParent() != null) {
            windowManager.removeView(floatingSearchView);
        }
        
        if (collapsedView != null && collapsedView.getParent() != null) {
            windowManager.removeView(collapsedView);
        }
        
        if (floatingSearchView != null) {
            floatingSearchView.destroy();
        }
    }
}
