package com.example.myapplication;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

public class FloatingSearchView extends LinearLayout {
    
    private final Context context;
    private final QuestionDatabase database;
    private SearchResultAdapter adapter;

    private EditText searchEditText;
    private RecyclerView resultsRecyclerView;
    private View resultsContainer;
    private View resultsResizeHandle;

    private OnCollapseListener onCollapseListener;

    // 拖拽相关
    private final WindowManager windowManager;
    private int initialX;
    private int initialY;
    private float initialTouchX;
    private float initialTouchY;

    // 调整大小相关
    private boolean isResultsResizing = false;
    private final int minResultsHeight;
    private final int maxResultsHeight;
    
    public interface OnCollapseListener {
        void onCollapse();
    }
    
    public FloatingSearchView(Context context) {
        super(context);
        this.context = context;
        this.database = new QuestionDatabase(context);
        this.windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

        // 初始化调整大小的限制值（dp转像素）
        float density = context.getResources().getDisplayMetrics().density;
        minResultsHeight = (int) (100 * density);
        maxResultsHeight = (int) (500 * density);

        initView();
        setupSearch();
        setupDragListener();
        setupResultsResizeListener();
    }
    
    private void initView() {
        LayoutInflater inflater = LayoutInflater.from(context);
        View view = inflater.inflate(R.layout.floating_search_view, this, true);
        
        searchEditText = view.findViewById(R.id.searchEditText);
        ImageView collapseButton = view.findViewById(R.id.collapseButton);
        resultsRecyclerView = view.findViewById(R.id.resultsRecyclerView);
        resultsContainer = view.findViewById(R.id.resultsContainer);
        resultsResizeHandle = view.findViewById(R.id.resultsResizeHandle);
        
        // 设置RecyclerView
        adapter = new SearchResultAdapter(new ArrayList<>());
        resultsRecyclerView.setLayoutManager(new LinearLayoutManager(context));
        resultsRecyclerView.setAdapter(adapter);

        // 设置初始高度
        setInitialResultsHeight();
        
        // 收缩按钮点击事件
        collapseButton.setOnClickListener(v -> {
            if (onCollapseListener != null) {
                onCollapseListener.onCollapse();
            }
        });
    }
    
    private void setupSearch() {
        // 设置输入框点击事件，显示输入法
        searchEditText.setOnClickListener(v -> {
            searchEditText.setFocusable(true);
            searchEditText.setFocusableInTouchMode(true);
            searchEditText.requestFocus();

            // 显示软键盘
            InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(searchEditText, InputMethodManager.SHOW_IMPLICIT);
            }
        });

        // 设置焦点变化监听
        searchEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.showSoftInput(searchEditText, InputMethodManager.SHOW_IMPLICIT);
                }
            }
        });

        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                String keyword = s.toString().trim();
                if (keyword.isEmpty()) {
                    adapter.updateResults(new ArrayList<>());
                    resultsContainer.setVisibility(GONE);
                } else {
                    searchQuestions(keyword);
                }
            }
        });
    }
    
    private void searchQuestions(String keyword) {
        // 在后台线程执行搜索
        new Thread(() -> {
            List<QuestionDatabase.Question> results = database.searchQuestions(keyword);
            
            // 在主线程更新UI
            post(() -> {
                adapter.updateResults(results);
                if (results.isEmpty()) {
                    resultsContainer.setVisibility(GONE);
                } else {
                    resultsContainer.setVisibility(VISIBLE);
                }
            });
        }).start();
    }
    
    private void setupDragListener() {
        View titleBar = findViewById(R.id.titleBar);
        titleBar.setOnTouchListener(new View.OnTouchListener() {
            private WindowManager.LayoutParams dragLayoutParams;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        if (getParent() != null) {
                            dragLayoutParams = (WindowManager.LayoutParams) getLayoutParams();
                            initialX = dragLayoutParams.x;
                            initialY = dragLayoutParams.y;
                            initialTouchX = event.getRawX();
                            initialTouchY = event.getRawY();
                        }
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        if (dragLayoutParams != null) {
                            dragLayoutParams.x = initialX + (int) (event.getRawX() - initialTouchX);
                            dragLayoutParams.y = initialY + (int) (event.getRawY() - initialTouchY);
                            windowManager.updateViewLayout(FloatingSearchView.this, dragLayoutParams);
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                        v.performClick();
                        return true;
                }
                return false;
            }
        });
    }



    private void setupResultsResizeListener() {
        resultsResizeHandle.setOnTouchListener(new View.OnTouchListener() {
            private int initialHeight;
            private float initialTouchY;

            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        initialHeight = resultsRecyclerView.getLayoutParams().height;
                        initialTouchY = event.getRawY();
                        isResultsResizing = true;
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        if (isResultsResizing) {
                            float deltaY = event.getRawY() - initialTouchY;
                            int newHeight = initialHeight + (int) deltaY;

                            // 限制在最小和最大高度范围内
                            newHeight = Math.max(minResultsHeight, Math.min(maxResultsHeight, newHeight));

                            // 更新RecyclerView的高度
                            LinearLayout.LayoutParams params =
                                (LinearLayout.LayoutParams) resultsRecyclerView.getLayoutParams();
                            params.height = newHeight;
                            resultsRecyclerView.setLayoutParams(params);

                            // 强制重新布局
                            resultsRecyclerView.requestLayout();
                            resultsContainer.requestLayout();
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                        isResultsResizing = false;
                        v.performClick();
                        return true;
                }
                return false;
            }
        });
    }

    private void setInitialResultsHeight() {
        // 设置RecyclerView的初始高度为中等大小
        float density = context.getResources().getDisplayMetrics().density;
        int initialHeight = (int) (200 * density); // 200dp

        LinearLayout.LayoutParams params =
            (LinearLayout.LayoutParams) resultsRecyclerView.getLayoutParams();
        params.height = initialHeight;
        resultsRecyclerView.setLayoutParams(params);
    }

    public void setOnCollapseListener(OnCollapseListener listener) {
        this.onCollapseListener = listener;
    }

    public void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && searchEditText != null) {
            imm.hideSoftInputFromWindow(searchEditText.getWindowToken(), 0);
            searchEditText.clearFocus();
        }
    }

    public void destroy() {
        hideKeyboard();
        if (database != null) {
            database.close();
        }
    }
}
