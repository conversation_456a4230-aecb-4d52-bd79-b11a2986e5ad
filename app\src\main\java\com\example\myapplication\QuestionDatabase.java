package com.example.myapplication;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class QuestionDatabase extends SQLiteOpenHelper {

    private static final String TAG = "QuestionDatabase";
    private static final String DATABASE_NAME = "questions.db";
    private static final int DATABASE_VERSION = 2; // 增加版本号以重新创建表
    
    // 表名和字段
    private static final String TABLE_QUESTIONS = "questions";
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_QUESTION = "question";
    private static final String COLUMN_ANSWER = "answer";
    private static final String COLUMN_CATEGORY = "category";
    private static final String COLUMN_DIFFICULTY = "difficulty";
    
    public QuestionDatabase(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onOpen(SQLiteDatabase db) {
        super.onOpen(db);
        // 确保数据库使用UTF-8编码
        db.execSQL("PRAGMA encoding = 'UTF-8'");
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        // 设置数据库编码为UTF-8
        db.execSQL("PRAGMA encoding = 'UTF-8'");

        String createTable = "CREATE TABLE " + TABLE_QUESTIONS + " (" +
            COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
            COLUMN_QUESTION + " TEXT NOT NULL COLLATE NOCASE, " +
            COLUMN_ANSWER + " TEXT COLLATE NOCASE, " +
            COLUMN_CATEGORY + " TEXT COLLATE NOCASE, " +
            COLUMN_DIFFICULTY + " TEXT COLLATE NOCASE" +
            ")";
        db.execSQL(createTable);
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_QUESTIONS);
        onCreate(db);
    }
    
    // 清空所有题目
    public void clearAllQuestions() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.delete(TABLE_QUESTIONS, null, null);
        db.close();
    }

    // 批量添加题目
    public void addQuestions(List<Question> questions) {
        SQLiteDatabase db = this.getWritableDatabase();
        db.beginTransaction();
        
        try {
            for (Question question : questions) {
                ContentValues values = new ContentValues();
                values.put(COLUMN_QUESTION, question.getQuestion());
                values.put(COLUMN_ANSWER, question.getAnswer());
                values.put(COLUMN_CATEGORY, question.getCategory());
                values.put(COLUMN_DIFFICULTY, question.getDifficulty());
                
                db.insert(TABLE_QUESTIONS, null, values);
            }
            db.setTransactionSuccessful();
        } finally {
            db.endTransaction();
            db.close();
        }
    }
    
    // 搜索题目
    public List<Question> searchQuestions(String keyword) {
        List<Question> questions = new ArrayList<>();

        if (keyword == null || keyword.trim().isEmpty()) {
            return questions;
        }

        Log.d(TAG, "搜索关键字: " + keyword);
        SQLiteDatabase db = this.getReadableDatabase();

        String selection = COLUMN_QUESTION + " LIKE ? OR " +
                          COLUMN_ANSWER + " LIKE ? OR " +
                          COLUMN_CATEGORY + " LIKE ?";
        String[] selectionArgs = {
            "%" + keyword + "%",
            "%" + keyword + "%",
            "%" + keyword + "%"
        };
        
        Cursor cursor = db.query(TABLE_QUESTIONS, null, selection, selectionArgs, 
                                null, null, null, "50"); // 限制返回50条结果
        
        if (cursor.moveToFirst()) {
            do {
                Question question = new Question();

                String questionText = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_QUESTION));
                String answerText = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_ANSWER));
                String categoryText = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CATEGORY));
                String difficultyText = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_DIFFICULTY));

                question.setQuestion(questionText);
                question.setAnswer(answerText);
                question.setCategory(categoryText);
                question.setDifficulty(difficultyText);

                Log.d(TAG, "搜索结果: " + questionText + " | " + answerText);
                questions.add(question);
            } while (cursor.moveToNext());
        }

        cursor.close();
        db.close();

        Log.d(TAG, "搜索完成，找到 " + questions.size() + " 条结果");
        return questions;
    }
    
    // 获取题目总数
    public int getQuestionCount() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_QUESTIONS, null);
        
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        
        cursor.close();
        db.close();
        
        return count;
    }

    // 题目数据模型
    public static class Question {
        private String question;
        private String answer;
        private String category;
        private String difficulty;
        
        public String getQuestion() { return question; }
        public void setQuestion(String question) { this.question = question; }
        
        public String getAnswer() { return answer; }
        public void setAnswer(String answer) { this.answer = answer; }
        
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        
        public String getDifficulty() { return difficulty; }
        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }
    }
}
